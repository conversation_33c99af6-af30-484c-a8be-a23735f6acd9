{{-- resources/views/livewire/dashboard/dashboard.blade.php --}}
@section('styles')
        <link href="https://cdn.jsdelivr.net/npm/gridstack@12.1.2/dist/gridstack.min.css" rel="stylesheet">
        <link rel="preconnect" href="https://rsms.me/">
        <link rel="stylesheet" href="https://rsms.me/inter/inter.css">
        <style type="text/css">
            #layout-sidenav { display: none; }
            .layout-sidenav-toggle { display: none; }
            body {
                font-family: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
                background-color: #f4f7f9;
                color: #333;
                margin: 0;
                line-height: 1.6;
            }
            .dashboard-container {
                max-width: 1600px;
                margin: 0 auto;
                padding: 20px;
            }
            .control-bar {
                margin-bottom: 25px;
                display: flex;
                gap: 10px;
                align-items: center;
                flex-wrap: wrap;
            }
            .unsaved-changes-warning {
                margin-left: auto;
                padding: 8px 12px;
                background-color: #fff3cd;
                color: #856404;
                border: 1px solid #ffeeba;
                border-radius: 6px;
                font-size: 0.9rem;
                font-weight: 500;
                display: flex;
                align-items: center;
                gap: 8px;
            }
            .unsaved-changes-warning svg {
                width: 18px;
                height: 18px;
                color: #856404;
            }
            .btn {
                padding: 10px 18px;
                border: none;
                border-radius: 6px;
                font-size: 0.95rem;
                font-weight: 500;
                cursor: pointer;
                transition: background-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out, transform 0.1s ease;
                text-decoration: none;
                display: inline-flex;
                align-items: center;
                justify-content: center;
                gap: 8px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.05);
                line-height: 1.5;
            }
            .btn:hover { transform: translateY(-1px); }
            .btn:active { transform: translateY(0px); }
            .btn svg { width: 18px; height: 18px; }
            .btn-primary { background-color: #007bff; color: white; }
            .btn-primary:hover { background-color: #0069d9; box-shadow: 0 4px 8px rgba(0,0,0,0.1); }
            .btn-success { background-color: #28a745; color: white; }
            .btn-success:hover { background-color: #218838; box-shadow: 0 4px 8px rgba(0,0,0,0.1); }
            .btn-info { background-color: #17a2b8; color: white; }
            .btn-info:hover { background-color: #138496; box-shadow: 0 4px 8px rgba(0,0,0,0.1); }
            .btn-warning { background-color: #ffc107; color: #212529; }
            .btn-warning:hover { background-color: #e0a800; box-shadow: 0 4px 8px rgba(0,0,0,0.1); }
            .btn-secondary { background-color: #6c757d; color: white; }
            .btn-secondary:hover { background-color: #5a6268; box-shadow: 0 4px 8px rgba(0,0,0,0.1); }
            .btn-outline-secondary { background-color: #fff; color: #6c757d; border: 1px solid #6c757d; }
            .btn-outline-secondary:hover { background-color: #f8f9fa; color: #5a6268; }
            .btn-icon { padding: 8px; min-width: auto; }
            .btn-icon svg { width: 16px; height: 16px; }
            .import-export-container { position: relative; display: inline-flex; gap: 10px; }
            .import-form-popup, .widget-form-popup {
                position: absolute;
                background-color: #ffffff;
                min-width: 380px;
                box-shadow: 0px 10px 25px 0px rgba(0,0,0,0.15);
                border-radius: 8px;
                padding: 24px;
                z-index: 99999;
                margin-top: 8px;
                border: 1px solid #dee2e6;
                display: none;
                top: 100%;
                left: 0;
            }
            .import-form-popup h3, .widget-form-popup h3 {
                margin-top: 0;
                margin-bottom: 20px;
                font-size: 1.2rem;
                font-weight: 600;
                color: #343a40;
            }
            .import-form-popup .form-group, .widget-form-popup .form-group {
                margin-bottom: 18px;
            }
            .import-form-popup label, .widget-form-popup label {
                display: block;
                margin-bottom: 6px;
                font-weight: 500;
                font-size: 0.875rem;
                color: #495057;
            }
            .import-form-popup input[type="file"], .widget-form-popup input[type="text"],
            .widget-form-popup input[type="number"], .widget-form-popup select {
                width: 100%;
                padding: 10px 12px;
                border: 1px solid #ced4da;
                border-radius: 5px;
                box-sizing: border-box;
                font-size: 0.9rem;
                background-color: #fff;
            }
            .widget-form-popup input[type="text"]:focus, .widget-form-popup input[type="number"]:focus,
            .widget-form-popup select:focus {
                border-color: #80bdff;
                outline: 0;
                box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
            }
            .import-form-popup .help-text, .widget-form-popup .help-text {
                font-size: 0.8rem;
                color: #6c757d;
                margin-top: 4px;
            }
            .import-form-popup .form-actions, .widget-form-popup .form-actions {
                display: flex;
                justify-content: flex-end;
                gap: 10px;
                margin-top: 24px;
            }
            .import-form-popup .error-message, .widget-form-popup .error-message {
                font-size: 0.8rem;
                color: #dc3545;
                margin-top: 4px;
            }
            .add-widget-form-popup-container { position: relative; display: inline-block; }
            .widget-form-popup .input-group { display: flex; gap: 15px; }
            .widget-form-popup .input-group > div { flex: 1; }
            .grid-stack-item-content {
                background-color: #ffffff;
                border-radius: 8px;
                box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
                overflow: hidden;
                display: flex;
                flex-direction: column;
                color: #333;
                border: 1px solid #e9ecef;
                transition: box-shadow 0.2s ease-in-out;
            }
            .grid-stack-item.grid-stack-placeholder > .grid-stack-item-content {
                border: 2px dashed #007bff !important;
                background: rgba(0,123,255,0.05) !important;
            }
            .widget-header {
                padding: 10px 15px;
                border-bottom: 1px solid #f1f3f5;
                display: flex;
                justify-content: space-between;
                align-items: center;
                background-color: #fcfdff;
                cursor: move;
            }
            .widget-title {
                font-size: 0.95rem;
                font-weight: 600;
                margin: 0;
                color: #343a40;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
            .widget-controls { display: flex; gap: 4px; }
            .btn-widget-edit, .btn-widget-reload {
                background: transparent;
                color: #adb5bd;
                border: none;
                padding: 6px;
                cursor: pointer;
            }
            .btn-widget-edit:hover { color: #007bff; background-color: #e7f3ff; }
            .btn-widget-reload:hover { color: #ffc107; background-color: #fff6e0; }
            .widget-controls .btn-widget-delete {
                background: transparent;
                color: #adb5bd;
                border: none;
                padding: 6px;
                cursor: pointer;
            }
            .widget-controls .btn-widget-delete:hover { color: #dc3545; background-color: #ffe3e6; }
            .widget-controls .btn-widget-delete svg, .widget-controls .btn-widget-edit svg,
            .widget-controls .btn-widget-reload svg {
                width: 16px;
                height: 16px;
                display: block;
            }
            .widget-body {
                padding: 10px;
                flex-grow: 1;
                display: flex;
                flex-direction: column;
                font-size: 0.9rem;
                overflow: hidden;
                min-height: 0;
            }
            .widget-meta-info {
                font-size: 0.75rem;
                color: #6c757d;
                display: flex;
                gap: 8px;
                align-items: center;
            }
            .widget-meta-info .meta-item { display: flex; align-items: center; gap: 4px; }
            .widget-meta-info span { font-weight: 500; color: #495057; }
            .widget-chart-placeholder {
                flex-grow: 1;
                display: flex;
                flex-direction: column;
                align-items: stretch;
                justify-content: flex-start;
                background-color: transparent;
                border: none;
                border-radius: 6px;
                color: #6c757d;
                font-size: 0.9rem;
                min-height: 0;
                padding: 0;
                text-align: center;
                overflow: hidden;
            }
            .grid-stack-skeleton {
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
                gap: 16px;
            }
            .skeleton-item {
                background-color: #e9ecef;
                border-radius: 8px;
                animation: pulse 1.8s cubic-bezier(0.4, 0, 0.6, 1) infinite;
                height: 160px;
            }
            .skeleton-item:nth-child(odd) { height: 220px; }
            .skeleton-item:nth-child(3n) { height: 180px; }
            @keyframes pulse {
                0%, 100% { opacity: 1; }
                50% { opacity: 0.7; }
            }
            .grid-stack-loading { position: relative; }
            .grid-stack-loading::after {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-color: rgba(255, 255, 255, 0.8);
                z-index: 999;
                border-radius: 8px;
            }
            .loading-skeleton-overlay {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                z-index: 1000;
                display: none;
                background-color: rgba(244, 247, 249, 0.9);
            }
            .loading-skeleton-overlay.show { display: block; }
            .loading-skeleton-grid {
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
                gap: 16px;
                padding: inherit;
                height: 100%;
            }
            .loading-skeleton-message {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background-color: white;
                padding: 20px 30px;
                border-radius: 8px;
                box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
                display: flex;
                align-items: center;
                gap: 12px;
                z-index: 1001;
            }
            .loading-skeleton-message svg {
                width: 24px;
                height: 24px;
                animation: spin 1s linear infinite;
                color: #007bff;
            }
            .loading-skeleton-message span { font-weight: 500; color: #343a40; }
            .loading-skeleton-item {
                background-color: #ffffff;
                border-radius: 8px;
                box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
                border: 1px solid #e9ecef;
                overflow: hidden;
                animation: pulse 1.8s cubic-bezier(0.4, 0, 0.6, 1) infinite;
            }
            .loading-skeleton-header {
                padding: 15px;
                border-bottom: 1px solid #f1f3f5;
                background-color: #fcfdff;
            }
            .loading-skeleton-title {
                height: 20px;
                background-color: #e9ecef;
                border-radius: 4px;
                width: 60%;
                animation: pulse 1.8s cubic-bezier(0.4, 0, 0.6, 1) infinite;
            }
            .loading-skeleton-body { padding: 20px; }
            .loading-skeleton-content {
                height: 100px;
                background-color: #f8f9fa;
                border-radius: 6px;
                animation: pulse 1.8s cubic-bezier(0.4, 0, 0.6, 1) infinite;
            }
            @keyframes spin {
                from { transform: rotate(0deg); }
                to { transform: rotate(360deg); }
            }
            .no-widgets-message {
                text-align: center;
                padding: 50px 20px;
                background-color: #ffffff;
                border: 1px solid #e9ecef;
                border-radius: 8px;
                margin-top: 30px;
                box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            }
            .no-widgets-message .icon {
                width: 60px;
                height: 60px;
                color: #adb5bd;
                margin-bottom: 20px;
            }
            .no-widgets-message h3 {
                font-size: 1.4rem;
                font-weight: 600;
                color: #343a40;
                margin-bottom: 12px;
            }
            .no-widgets-message p {
                font-size: 0.95rem;
                color: #6c757d;
                line-height: 1.7;
            }
            .sr-only {
                position: absolute;
                width: 1px;
                height: 1px;
                padding: 0;
                margin: -1px;
                overflow: hidden;
                clip: rect(0, 0, 0, 0);
                white-space: nowrap;
                border-width: 0;
            }
            .dashboard-status-indicator {
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 0.8rem;
                font-weight: 500;
            }
            .dashboard-status-active {
                background-color: #d4edda;
                color: #155724;
                border: 1px solid #c3e6cb;
            }
            .dashboard-status-inactive {
                background-color: #f8d7da;
                color: #721c24;
                border: 1px solid #f5c6cb;
            }
            .custom-select-container { position: relative; width: 100%; }
            .custom-select-container select { display: none !important; }
            .select-selected {
                background-color: #fff;
                border: 1px solid #ccc;
                padding: 10px;
                cursor: pointer;
                border-radius: 4px;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }
            .select-selected:after { content: "▼"; font-size: 12px; color: #666; }
            .select-selected.select-arrow-active:after { content: "▲"; }
            .select-items {
                position: absolute;
                background-color: #fff;
                border: 1px solid #ccc;
                border-top: none;
                border-radius: 0 0 4px 4px;
                top: 100%;
                left: 0;
                right: 0;
                z-index: 9999;
                max-height: 300px;
                overflow-y: auto;
                display: none;
            }
            .select-search {
                width: 100%;
                padding: 10px;
                border: none;
                border-bottom: 1px solid #eee;
                box-sizing: border-box;
                outline: none;
            }
            .select-item {
                padding: 10px;
                cursor: pointer;
                transition: background-color 0.2s;
            }
            .select-item:hover { background-color: #f1f1f1; }
            .select-item.select-hide { display: none; }
            .select-item.same-as-selected { background-color: #e0e0e0; }
            .no-results { padding: 10px; color: #666; text-align: center; }
            .widget-content-row {
                display: flex;
                height: 100%;
                gap: 8px;
                align-items: stretch;
            }
            .widget-content-row.reverse { flex-direction: row-reverse; }
            .widget-graph-column, .widget-image-column {
                flex: 1;
                height: 100%;
                overflow: hidden;
                min-width: 0;
            }
            .widget-image-column {
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 8px;
                box-sizing: border-box;
            }
            .widget-image-container {
                width: 100%;
                height: 100%;
                max-width: 100%;
                max-height: 100%;
                position: relative;
                overflow: hidden;
                display: flex;
                align-items: center;
                justify-content: center;
                box-sizing: border-box;
            }
            .widget-image-container img {
                max-width: 100% !important;
                max-height: 100% !important;
                width: auto !important;
                height: auto !important;
                object-fit: contain !important;
                object-position: center !important;
                border-radius: 6px;
                display: block !important;
                flex-shrink: 1 !important;
            }
            .custom-widget-body {
                height: 100%;
                display: flex;
                flex-direction: column;
                overflow: hidden;
            }
            /* Status-specific styles */
            .select-status-active {
                background-color: #28a745 !important;
                border-color: #28a745 !important;
                box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.2);
            }

            .select-status-active:hover {
                background-color: #218838 !important;
                border-color: #1e7e34 !important;
            }

            .select-status-partly-active {
                background-color: #bf9006 !important;
                border-color: #8c6805 !important;
                box-shadow: 0 0 0 3px rgba(255, 193, 7, 0.2);
            }

            .select-status-partly-active:hover {
                background-color: #ac8004 !important;
                border-color: #634c04 !important;
            }

            .select-status-inactive {
                background-color: #dc3545 !important;
                border-color: #dc3545 !important;
                box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.2);
            }

            .select-status-inactive:hover {
                background-color: #c82333 !important;
                border-color: #bd2130 !important;
            }
        </style>
@endsection

    <div class="dashboard-container">
        <div class="control-bar">
            <a href="{{ url($path) }}" class="btn btn-info" title="{{ __('designer.back_to_dashboard') }}">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M9 15L3 9m0 0l6-6M3 9h12a6 6 0 010 12h-3" />
                </svg>
                {{ __('designer.back_to_dashboard') }}
            </a>

            <div class="add-widget-form-popup-container">
                <button id="toggleAddWidgetFormBtn" type="button" class="btn btn-success">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
                    </svg>
                    {{ __('designer.add_widget') }}
                    <svg id="addWidgetCaret" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="ml-1 h-5 w-5 transition-transform duration-200">
                        <path fill-rule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 10.94l3.71-3.71a.75.75 0 111.06 1.06l-4.25 4.25a.75.75 0 01-1.06 0L5.23 8.29a.75.75 0 01.02-1.06z" clip-rule="evenodd" />
                    </svg>
                </button>

                <div id="addWidgetFormPopup" class="widget-form-popup">
                    <h3>{{ __('designer.add_new_widget') }}</h3>
                    <form id="newWidgetForm">
                        <div class="form-group">
                            <label for="new_widget_title">{{ __('designer.title') }}</label>
                            <input type="text" id="new_widget_title" name="title" required />
                        </div>
                        <div class="form-group">
                            <label for="new_widget_diagram_type">{{ __('designer.diagram_type') }}</label>
                            <select id="new_widget_diagram_type" name="diagram_type" required>
                                <option value="">{{ __('designer.select_type') }}</option>
                                @foreach (\App\Enums\DiagramType::cases() as $type)
                                    <option value="{{ $type->value }}">{{ $type->label() }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="new_widget_pool_type">{{ __('designer.data_pool') }}</label>
                            <select id="new_widget_pool_type" name="pool_type" required>
                                <option disabled="disabled" selected>{{ __('designer.select_pool') }}</option>
                                @foreach ($pools ?? [] as $pool)
                                    <option value="{{ $pool->id }}" >{{ $pool->pool_name }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="form-group input-group">
                            <div>
                                <label for="new_widget_width">{{ __('designer.width_cols') }}</label>
                                <input type="number" id="new_widget_width" name="width" min="1" max="12" value="4" required />
                            </div>
                            <div>
                                <label for="new_widget_height">{{ __('designer.height_rows') }}</label>
                                <input type="number" id="new_widget_height" name="height" min="1" max="10" value="2" required />
                            </div>
                        </div>
                        <div class="form-actions">
                            <button type="button" id="cancelAddWidgetBtn" class="btn btn-outline-secondary">{{ __('designer.cancel') }}</button>
                            <button type="submit" class="btn btn-primary">{{ __('designer.add_widget') }}</button>
                        </div>
                    </form>
                </div>
            </div>

            <button wire:click="triggerSaveLayoutToDb" class="btn btn-primary" wire:loading.attr="disabled" wire:target="triggerSaveLayoutToDb">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M19 21H5a2 2 0 01-2-2V5a2 2 0 012-2h11l5 5v11a2 2 0 01-2 2z" />
                    <path stroke-linecap="round" stroke-linejoin="round" d="M17 21v-8H7v8M7 3v5h8" />
                </svg>
                <span wire:loading.remove wire:target="triggerSaveLayoutToDb">{{ __('designer.save') }}</span>
                <span wire:loading wire:target="triggerSaveLayoutToDb">{{ __('designer.saving_to_db') }}</span>
            </button>

            <div class="import-export-container">
                <button id="exportDashboardBtn" type="button" class="btn btn-secondary" title="{{ __('designer.export_dashboard_title') }}">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5m-13.5-9L12 3m0 0l4.5 4.5M12 3v13.5" />
                    </svg>
                    {{ __('designer.export') }}
                </button>

                <div style="position: relative;">
                    <button id="toggleImportFormBtn" type="button" class="btn btn-secondary" title="{{ __('designer.import_dashboard_title') }}">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5M16.5 12L12 16.5m0 0L7.5 12m4.5 4.5V3" />
                        </svg>
                        {{ __('designer.import') }}
                    </button>

                    <div id="importFormPopup" class="import-form-popup">
                        <h3>{{ __('designer.import_dashboard_config') }}</h3>
                        <form id="importDashboardForm">
                            <div class="form-group">
                                <label for="importFile">{{ __('designer.select_json_file') }}</label>
                                <input type="file" id="importFile" accept=".json" required>
                                <div class="help-text">{{ __('designer.select_json_help') }}</div>
                                <div id="fileError" class="error-message" style="display: none;"></div>
                                <div id="fileSelectedIndicator" class="help-text" style="color: #28a745; margin-top: 8px; display: none;">
                                    <svg style="display: inline-block; width: 16px; height: 16px;" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                    <span id="selectedFileName">{{ __('designer.file_selected', ['filename' => '']) }}</span>
                                </div>
                            </div>
                            <div class="form-actions">
                                <button type="button" id="cancelImportBtn" class="btn btn-outline-secondary">{{ __('designer.cancel') }}</button>
                                <button type="submit" id="importSubmitBtn" class="btn btn-primary">
                                    <span id="importBtnText">{{ __('designer.import_configuration') }}</span>
                                    <span id="importBtnLoading" style="display: none;">
                                        <svg style="display: inline-block; width: 16px; height: 16px; animation: spin 1s linear infinite;" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                        </svg>
                                        {{ __('designer.importing') }}
                                    </span>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            {{-- Only show status controls and Assigned Users button if a dashboard model is loaded and exists in DB --}}
            @if($dashboard && $dashboard->id)
                {{-- Simple Select Dropdown for Status --}}
                <select wire:model.live="dashboardStatus"
                        @class([
                            'btn btn-secondary',
                            'select-status-active text-white' => $dashboardStatus == 1,
                            'select-status-partly-active text-gray-900' => $dashboardStatus == 2,
                            'select-status-inactive text-white' => $dashboardStatus == 0,
                        ])
                        wire:loading.attr="disabled"
                        wire:target="dashboardStatus"
                        style="padding: 10px 18px; border: 1px solid #ced4da; border-radius: 6px; font-size: 0.95rem; font-weight: 500; cursor: pointer;">
                    <option value="1">{{ __('designer.status_active') }}</option>
                    <option value="2">{{ __('designer.status_partly_active') }}</option>
                    <option value="0">{{ __('designer.status_inactive') }}</option>
                </select>

                {{-- NEW: Assigned Users Button --}}
                @if($dashboardStatus == 2)
                    <a href="{{ route('dashboard.designer.assigned_users', ['dashboardId' => $dashboard->id]) }}" target="_blank" class="btn btn-primary">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 6a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0zM4.501 20.118a7.5 7.5 0 0114.998 0A17.933 17.933 0 0112 21.75c-2.676 0-5.216-.584-7.499-1.632z" />
                        </svg>
                        {{ __('designer.assigned_users') }}
                    </a>
                @endif
            @else
                {{-- Show a message if no dashboard is created/loaded --}}
                <span class="dashboard-status-indicator dashboard-status-inactive">
                    {{ __('designer.status_not_available') }}
                </span>
            @endif

            <div id="unsavedChangesWarning" class="unsaved-changes-warning" style="display: none;">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" /></svg>
                {{ __('designer.unsaved_changes') }}
            </div>
        </div>

        @if ($isInitialLoad)
            <div class="grid-stack-skeleton">
                @for ($i = 0; $i < 6; $i++) <div class="skeleton-item"></div> @endfor
            </div>
        @elseif (empty($dashboardData))
            <div class="no-widgets-message">
                <svg class="icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M2.25 13.5h3.86a2.25 2.25 0 012.012 1.244l.256.512a2.25 2.25 0 002.013 1.244h3.218a2.25 2.25 0 002.013-1.244l.256-.512a2.25 2.25 0 012.013-1.244h3.859m-19.5.338V18a2.25 2.25 0 002.25 2.25h15A2.25 2.25 0 0021.75 18v-4.162c0-.224-.034-.447-.1-.661L19.24 5.338a2.25 2.25 0 00-2.15-1.588H6.911a2.25 2.25 0 00-2.15 1.588L2.35 13.177a2.25 2.25 0 00-.1.661z" /></svg>
                <h3>{{ __('designer.dashboard_empty') }}</h3>
                <p>{{ __('designer.dashboard_empty_message') }}</p>
            </div>
        @else
            <div class="grid-container" style="position: relative;">
                <div id="loadingSkeletonOverlay" class="loading-skeleton-overlay" wire:loading.class="show" wire:target="triggerSaveLayoutToDb,toggleActivityStatus,importDashboard,persistLayout,removeWidget,addWidget,updateWidget,reloadWidgetSettings">
                    <div class="loading-skeleton-grid">
                        @for ($i = 0; $i < max(count($dashboardData), 6); $i++)
                            <div class="loading-skeleton-item">
                                <div class="loading-skeleton-header">
                                    <div class="loading-skeleton-title"></div>
                                </div>
                                <div class="loading-skeleton-body">
                                    <div class="loading-skeleton-content"></div>
                                </div>
                            </div>
                        @endfor
                    </div>
                    <div class="loading-skeleton-message">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                        </svg>
                        <span wire:loading wire:target="triggerSaveLayoutToDb">{{ __('designer.saving_layout') }}</span>
                        <span wire:loading wire:target="toggleActivityStatus">{{ __('designer.updating_status') }}</span>
                        <span wire:loading wire:target="importDashboard">{{ __('designer.importing_dashboard') }}</span>
                        <span wire:loading wire:target="persistLayout">{{ __('designer.updating_layout') }}</span>
                        <span wire:loading wire:target="removeWidget">{{ __('designer.removing_widget') }}</span>
                        <span wire:loading wire:target="addWidget">{{ __('designer.adding_widget') }}</span>
                        <span wire:loading wire:target="updateWidget">{{ __('designer.updating_widget') }}</span>
                        <span wire:loading wire:target="reloadWidgetSettings">{{ __('designer.reloading_settings') }}</span>
                    </div>
                </div>

                <div class="grid-stack" wire:key="dashboard-{{rand()}}"
                     wire:loading.class="grid-stack-loading"
                     wire:target="triggerSaveLayoutToDb,toggleActivityStatus,importDashboard,persistLayout,removeWidget,addWidget,updateWidget,reloadWidgetSettings"
                     style="display: none;">
                    @foreach ($dashboardData as $widget)
                        @php
                            $diagramType = strtolower($widget['diagram_type'] ?? '');
                        @endphp
                        <div class="grid-stack-item ui-resizable-autohide"
                             gs-x="{{ $widget['x'] ?? 0 }}"
                             gs-y="{{ $widget['y'] ?? 0 }}"
                             gs-w="{{ $widget['width'] ?? 4 }}"
                             gs-h="{{ $widget['height'] ?? 2 }}"
                             gs-id="{{ $widget['id'] }}"
                             data-widget-id="{{ $widget['id'] }}"
                             data-title="{{ $widget['title'] }}"
                             data-diagram-type="{{ $diagramType }}"
                             data-pool-type="{{ $widget['pool_type'] }}"
                             data-settings="{{ json_encode($widget['settings'] ?? []) }}"
                             wire:key="widget-{{ $widget['id'] }}">
                            <div class="grid-stack-item-content">
                                <div class="widget-header">
                                    <h3 class="widget-title" title="{{ $widget['title'] }}">{{ $widget['title'] }}</h3>

                                    <div class="widget-meta-info">
                                        <div class="meta-item">
                                            <span>{{ __('designer.type') }}:</span>
                                            <span>{{ ucfirst($diagramType) }}</span>
                                        </div>
                                        <span>|</span>
                                        <div class="meta-item">
                                            <span>{{ __('designer.pool') }}:</span>
                                            <span>{{ $pools->firstWhere('id', (int)$widget['pool_type'])?->pool_name ?? __('designer.unknown') }}</span>
                                        </div>
                                    </div>

                                    <div class="widget-controls">
                                        @if(!in_array($diagramType, ['biorythm', 'menu']))
                                            <a
                                                    href="{{ route('dashboard.designer.edit', ['dashboardId' => $dashboard->id, 'widgetId' => $widget['id']]) }}"
                                                    target="_blank"
                                                    class="btn btn-icon btn-widget-edit"
                                                    title="{{ __('designer.edit_widget') }}">
                                                <svg class="icon-edit" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L10.582 16.07a4.5 4.5 0 01-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 011.13-1.897l8.932-8.931zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0115.75 21H5.25A2.25 2.25 0 013 18.75V8.25A2.25 2.25 0 015.25 6H10" /></svg>
                                                <span class="sr-only">{{ __('designer.edit_widget') }} {{ $widget['title'] }}</span>
                                            </a>
                                            <button
                                                    onclick="reloadWidgetSettingsWithoutConfirmation('{{ $widget['id'] }}')"
                                                    type="button"
                                                    class="btn btn-icon btn-widget-reload"
                                                    title="{{ __('designer.reload_settings') }}">
                                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                                </svg>
                                                <span class="sr-only">{{ __('designer.reload_settings') }} {{ $widget['title'] }}</span>
                                            </button>
                                        @endif
                                        <button
                                                onclick="confirmRemoveWidget('{{ $widget['id'] }}')"
                                                type="button"
                                                class="btn btn-icon btn-widget-delete"
                                                title="{{ __('designer.delete_widget') }}">
                                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12.56 0c.342.052.682.107 1.022.166m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0" /></svg>
                                            <span class="sr-only">{{ __('designer.delete_widget') }} {{ $widget['title'] }}</span>
                                        </button>
                                    </div>
                                </div>
                                <div class="widget-body">
                                    <div class="widget-chart-placeholder">
                                        @if ($diagramType === 'biorythm')
                                            @include('Frontend.partials.includes.layout-biorhythmus')
                                        @elseif (in_array($diagramType, ['filters','menu','won_topic','frequency_generator']))
                                            <div class="custom-widget-body">
                                                @includeIf('Frontend.dashboard.partials._navigation', ['widget' => $widget, 'diagramType' => $diagramType])
                                            </div>
                                        @elseif ($diagramType === 'richtext')
                                            <div class="custom-widget-body">
                                                @includeIf('Frontend.dashboard.partials._graph', ['widget' => $widget, 'diagramType' => $diagramType])
                                            </div>
                                        @elseif (in_array($diagramType, ['bar', 'line', 'radar', 'polararea', 'progress']))
                                            <livewire:dashboard.color-analyses-add-to-cart :poolId="$widget['pool_type']" :wire:key="'color-analyses-add-to-cart-'.$widget['pool_type']. '-' . rand()">
                                                @if (!empty($widget['settings']['image']))
                                                    <div class="custom-widget-body">
                                                        <div class="widget-content-row {{ $widget['settings']['image_position'] === 'right' ? '' : 'reverse' }}">
                                                            <div class="widget-graph-column">
                                                                @includeIf('Frontend.dashboard.partials._graph', ['widget' => $widget, 'diagramType' => $diagramType])
                                                            </div>
                                                            <div class="widget-image-column">
                                                                <div class="widget-image-container">
                                                                    <img src="{{ asset('storage/images/' . $widget['settings']['image']) }}" alt="Widget Image" loading="lazy">
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                @else
                                                    <div class="custom-widget-body">
                                                        @includeIf('Frontend.dashboard.partials._graph', ['widget' => $widget, 'diagramType' => $diagramType])
                                                    </div>
                                        @endif
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        @endif
    </div>
@section('scripts')
        <script src="https://cdn.jsdelivr.net/npm/gridstack@12.1.2/dist/gridstack-all.min.js"></script>
        <script>
            window.dashboardTranslations = {
                unsavedChangesWarning: @json(__('designer.unsaved_changes_warning')),
                confirmDeleteWidget: @json(__('designer.confirm_delete_widget')),
                correctErrors: @json(__('designer.correct_errors')),
                gridstackError: @json(__('designer.gridstack_error')),
                noItemsToInitialize: @json(__('designer.no_items_to_initialize')),
                fileSelected: @json(__('designer.file_selected')),
                pleaseSelectFile: @json(__('designer.please_select_file')),
                pleaseSelectJson: @json(__('designer.please_select_json')),
                fileSizeExceeded: @json(__('designer.file_size_exceeded')),
                errorReadingFile: @json(__('designer.error_reading_file')),
                unknownTitle: @json(__('designer.unknown_title')),
                unknownWidget: @json(__('designer.unknown_widget')),
                titleLabel: @json(__('designer.title_label')),
                diagramTypeLabel: @json(__('designer.diagram_type_label')),
                poolTypeLabel: @json(__('designer.pool_type_label'))
            };

            class DashboardManager {
                constructor() {
                    this.hasUnsavedChanges = false;
                    this.unsavedMessage = window.dashboardTranslations.unsavedChangesWarning;
                    this.grid = null;
                    this.unsavedChangesHandler = new UnsavedChangesHandler(this);
                    this.widgetFormHandler = new WidgetFormHandler(this);
                    this.gridstackHandler = new GridstackHandler(this);
                    this.livewireHandler = new LivewireHandler(this);
                    this.importExportHandler = new ImportExportHandler(this);
                    this.init();
                }

                init() {
                    this.setupBeforeUnloadHandler();
                    this.gridstackHandler.initialize();
                    this.livewireHandler.setupEventListeners();
                    this.importExportHandler.setupEventListeners();
                }

                setUnsavedChanges(status) {
                    this.hasUnsavedChanges = status;
                    this.unsavedChangesHandler.updateWarningDisplay(status);
                }

                getCurrentLayout() {
                    if (!this.grid || typeof this.grid.save !== 'function') return [];
                    const savedData = this.grid.save(false, true);
                    const items = savedData.children || savedData;
                    return Array.isArray(items) ? items
                        .filter(item => item && item.id)
                        .map(item => this.mapGridItemToLayout(item)) : [];
                }

                mapGridItemToLayout(item) {
                    const el = this.grid.engine.nodes.find(n => n.id === item.id)?.el;
                    return {
                        id: item.id,
                        x: item.x,
                        y: item.y,
                        w: item.w,
                        h: item.h,
                        title: el?.dataset.title || window.dashboardTranslations.unknownTitle,
                        diagram_type: el?.dataset.diagramType || 'Bar',
                        pool_type: el?.dataset.poolType || 'Dashboard 1',
                        settings: el?.dataset.settings ? JSON.parse(el.dataset.settings) : {}
                    };
                }

                setupBeforeUnloadHandler() {
                    window.addEventListener('beforeunload', (event) => {
                        if (this.hasUnsavedChanges) {
                            event.preventDefault();
                            event.returnValue = this.unsavedMessage;
                            return this.unsavedMessage;
                        }
                    });
                }

                confirmRemoveWidget(widgetId) {
                    if (window.confirm(window.dashboardTranslations.confirmDeleteWidget)) {
                        const gridItems = this.grid?.getGridItems() || [];
                        gridItems.forEach(item => {
                            item.classList.remove('ui-resizable-autohide');
                            const mouseLeaveEvent = new MouseEvent('mouseleave', { bubbles: true });
                            item.dispatchEvent(mouseLeaveEvent);
                        });
                        const currentLayout = this.getCurrentLayout();
                        Livewire.dispatch('jsTriggerRemoveWidgetWithLayout', [{ widgetId, currentLayout }]);
                        setTimeout(() => { this.gridstackHandler.fixResizeHandles(); }, 100);
                        setTimeout(() => { this.gridstackHandler.fixResizeHandles(); }, 300);
                        setTimeout(() => { this.gridstackHandler.fixResizeHandles(); }, 600);
                    }
                }

                reloadWidgetSettingsWithoutConfirmation(widgetId) {
                    const currentLayout = this.getCurrentLayout();
                    Livewire.dispatch('reloadWidgetSettings', [{ widgetId, currentLayout }]);
                    setTimeout(() => { this.gridstackHandler.fixResizeHandles(); }, 200);
                }
            }

            class UnsavedChangesHandler {
                constructor(dashboardManager) {
                    this.dashboardManager = dashboardManager;
                    this.warningElement = document.getElementById('unsavedChangesWarning');
                }

                updateWarningDisplay(hasUnsavedChanges) {
                    if (this.warningElement) {
                        this.warningElement.style.display = hasUnsavedChanges ? 'flex' : 'none';
                    }
                }
            }

            class WidgetFormHandler {
                constructor(dashboardManager) {
                    this.dashboardManager = dashboardManager;
                    this.elements = {
                        popup: document.getElementById('addWidgetFormPopup'),
                        toggleBtn: document.getElementById('toggleAddWidgetFormBtn'),
                        caret: document.getElementById('addWidgetCaret'),
                        form: document.getElementById('newWidgetForm'),
                        cancelBtn: document.getElementById('cancelAddWidgetBtn')
                    };
                    this.setupEventListeners();
                }

                setupEventListeners() {
                    this.setupToggleButton();
                    this.setupCancelButton();
                    this.setupFormSubmission();
                }

                setupToggleButton() {
                    if (!this.elements.toggleBtn || !this.elements.popup || !this.elements.caret) return;
                    this.elements.toggleBtn.addEventListener('click', () => {
                        this.toggleFormVisibility();
                    });
                }

                setupCancelButton() {
                    if (!this.elements.cancelBtn) return;
                    this.elements.cancelBtn.addEventListener('click', () => {
                        this.hideForm();
                        this.resetForm();
                    });
                }

                setupFormSubmission() {
                    if (!this.elements.form) return;
                    this.elements.form.addEventListener('submit', (event) => {
                        event.preventDefault();
                        this.handleFormSubmission();
                    });
                }

                toggleFormVisibility() {
                    const isVisible = this.elements.popup.style.display === 'block';
                    this.elements.popup.style.display = isVisible ? 'none' : 'block';
                    this.elements.caret.classList.toggle('rotate-180', !isVisible);
                }

                hideForm() {
                    if (this.elements.popup && this.elements.caret) {
                        this.elements.popup.style.display = 'none';
                        this.elements.caret.classList.remove('rotate-180');
                    }
                }

                resetForm() {
                    if (this.elements.form) {
                        this.elements.form.reset();
                    }
                }

                handleFormSubmission() {
                    const formData = new FormData(this.elements.form);
                    const newWidgetData = Object.fromEntries(formData.entries());
                    const currentLayout = this.dashboardManager.getCurrentLayout();
                    Livewire.dispatch('jsSubmitNewWidgetWithLayout', [{ newWidgetData, currentLayout }]);
                    this.resetForm();
                    this.hideForm();
                    setTimeout(() => { this.dashboardManager.gridstackHandler.fixResizeHandles(); }, 200);
                }
            }

            class ImportExportHandler {
                constructor(dashboardManager) {
                    this.dashboardManager = dashboardManager;
                    this.selectedFile = null;
                    this.elements = {
                        exportBtn: document.getElementById('exportDashboardBtn'),
                        importToggleBtn: document.getElementById('toggleImportFormBtn'),
                        importPopup: document.getElementById('importFormPopup'),
                        importCancelBtn: document.getElementById('cancelImportBtn'),
                        importForm: document.getElementById('importDashboardForm'),
                        fileInput: document.getElementById('importFile'),
                        fileIndicator: document.getElementById('fileSelectedIndicator'),
                        fileName: document.getElementById('selectedFileName'),
                        fileError: document.getElementById('fileError'),
                        submitBtn: document.getElementById('importSubmitBtn'),
                        submitBtnText: document.getElementById('importBtnText'),
                        submitBtnLoading: document.getElementById('importBtnLoading')
                    };
                }

                setupEventListeners() {
                    this.setupExportButton();
                    this.setupImportToggleButton();
                    this.setupImportCancelButton();
                    this.setupFileInputListener();
                    this.setupImportFormSubmit();
                    this.setupDownloadListener();
                    this.setupImportSuccessListener();
                }

                setupExportButton() {
                    if (!this.elements.exportBtn) return;
                    this.elements.exportBtn.addEventListener('click', () => {
                        const currentLayout = this.dashboardManager.getCurrentLayout();
                        Livewire.dispatch('exportDashboard', [{ currentLayout }]);
                    });
                }

                setupImportToggleButton() {
                    if (!this.elements.importToggleBtn || !this.elements.importPopup) return;
                    this.elements.importToggleBtn.addEventListener('click', () => {
                        const isVisible = this.elements.importPopup.style.display === 'block';
                        this.elements.importPopup.style.display = isVisible ? 'none' : 'block';
                    });
                }

                setupImportCancelButton() {
                    if (!this.elements.importCancelBtn || !this.elements.importPopup) return;
                    this.elements.importCancelBtn.addEventListener('click', () => {
                        this.closeImportPopup();
                    });
                }

                setupFileInputListener() {
                    if (!this.elements.fileInput) return;
                    this.elements.fileInput.addEventListener('change', (e) => {
                        const file = e.target.files[0];
                        this.selectedFile = file;

                        if (file) {
                            if (!file.name.endsWith('.json')) {
                                this.showFileError(window.dashboardTranslations.pleaseSelectJson);
                                this.elements.fileIndicator.style.display = 'none';
                                this.selectedFile = null;
                                return;
                            }

                            if (file.size > 2 * 1024 * 1024) {
                                this.showFileError(window.dashboardTranslations.fileSizeExceeded);
                                this.elements.fileIndicator.style.display = 'none';
                                this.selectedFile = null;
                                return;
                            }

                            this.hideFileError();
                            this.elements.fileName.textContent = window.dashboardTranslations.fileSelected.replace(':filename', file.name);
                            this.elements.fileIndicator.style.display = 'block';
                        } else {
                            this.elements.fileIndicator.style.display = 'none';
                            this.selectedFile = null;
                        }
                    });
                }

                setupImportFormSubmit() {
                    if (!this.elements.importForm) return;
                    this.elements.importForm.addEventListener('submit', async (e) => {
                        e.preventDefault();

                        if (!this.selectedFile) {
                            this.showFileError(window.dashboardTranslations.pleaseSelectFile);
                            return;
                        }

                        this.elements.submitBtn.disabled = true;
                        this.elements.submitBtnText.style.display = 'none';
                        this.elements.submitBtnLoading.style.display = 'inline-block';

                        try {
                            const fileContent = await this.readFileContent(this.selectedFile);
                            Livewire.dispatch('importDashboard', [fileContent]);
                        } catch (error) {
                            this.showFileError(window.dashboardTranslations.errorReadingFile.replace(':error', error.message));
                            this.resetSubmitButton();
                        }
                    });
                }

                readFileContent(file) {
                    return new Promise((resolve, reject) => {
                        const reader = new FileReader();
                        reader.onload = (e) => resolve(e.target.result);
                        reader.onerror = (e) => reject(new Error('Failed to read file'));
                        reader.readAsText(file);
                    });
                }

                setupDownloadListener() {
                    Livewire.on('downloadDashboardExport', (event) => {
                        const data = event[0];
                        this.downloadJSON(data.content, data.filename);
                    });
                }

                setupImportSuccessListener() {
                    Livewire.on('importSuccess', () => {
                        this.closeImportPopup();
                        this.resetSubmitButton();
                    });

                    Livewire.on('livewire:error', () => {
                        this.resetSubmitButton();
                    });
                }

                closeImportPopup() {
                    if (this.elements.importPopup) {
                        this.elements.importPopup.style.display = 'none';
                    }
                    this.resetForm();
                }

                resetForm() {
                    if (this.elements.fileInput) {
                        this.elements.fileInput.value = '';
                    }
                    this.selectedFile = null;
                    this.elements.fileIndicator.style.display = 'none';
                    this.hideFileError();
                    this.resetSubmitButton();
                }

                resetSubmitButton() {
                    if (this.elements.submitBtn) {
                        this.elements.submitBtn.disabled = false;
                    }
                    if (this.elements.submitBtnText) {
                        this.elements.submitBtnText.style.display = 'inline';
                    }
                    if (this.elements.submitBtnLoading) {
                        this.elements.submitBtnLoading.style.display = 'none';
                    }
                }

                showFileError(message) {
                    if (this.elements.fileError) {
                        this.elements.fileError.textContent = message;
                        this.elements.fileError.style.display = 'block';
                    }
                }

                hideFileError() {
                    if (this.elements.fileError) {
                        this.elements.fileError.style.display = 'none';
                    }
                }

                downloadJSON(content, filename) {
                    const blob = new Blob([content], { type: 'application/json' });
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = filename;
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);
                }
            }

            class GridstackHandler {
                constructor(dashboardManager) {
                    this.dashboardManager = dashboardManager;
                    this.gridElement = null;
                    this.skeletonElement = document.getElementById('gridstack-init-skeleton');
                }

                initialize() {
                    this.updateWarningDisplay();
                    this.gridElement = document.querySelector('.grid-stack');

                    if (!this.gridElement) {
                        this.hideSkeletonAndWarn();
                        return;
                    }

                    const itemsExist = this.gridElement.querySelector('.grid-stack-item');
                    this.updateSkeletonDisplay(itemsExist);
                    this.destroyPreviousGrid();

                    if (!itemsExist && !window.isInitialLoad) {
                        this.handleEmptyGrid();
                        return;
                    }

                    if (itemsExist) {
                        this.gridElement.style.display = 'block';
                    }

                    this.initializeGrid();
                }

                updateWarningDisplay() {
                    this.dashboardManager.unsavedChangesHandler.updateWarningDisplay(
                        this.dashboardManager.hasUnsavedChanges
                    );
                }

                hideSkeletonAndWarn() {
                    if (this.skeletonElement) {
                        this.skeletonElement.style.display = 'none';
                    }
                }

                updateSkeletonDisplay(itemsExist) {
                    if (this.skeletonElement) {
                        this.skeletonElement.style.display =
                            (!itemsExist && !this.dashboardManager.grid) ? 'grid' : 'none';
                    }
                }

                destroyPreviousGrid() {
                    if (this.dashboardManager.grid?.el && document.body.contains(this.dashboardManager.grid.el)) {
                        try {
                            this.dashboardManager.grid.destroy(false);
                        } catch (e) {}
                    }
                }

                handleEmptyGrid() {
                    if (this.skeletonElement) {
                        this.skeletonElement.style.display = 'none';
                    }
                    this.gridElement.style.display = 'block';
                }

                initializeGrid() {
                    try {
                        this.dashboardManager.grid = GridStack.init({
                            cellHeight: 80,
                            margin: 10,
                            float: true,
                            animate: true,
                            draggable: {
                                handle: '.widget-header',
                                cancel: '.widget-controls button, .custom-select-container'
                            }
                        }, this.gridElement);

                        this.setupGridEventListeners();
                        this.dashboardManager.grid.enableResize(true);

                        if (this.skeletonElement) {
                            this.skeletonElement.style.display = 'none';
                        }
                    } catch (e) {
                        this.handleGridError(e);
                    }
                }

                setupGridEventListeners() {
                    const grid = this.dashboardManager.grid;

                    grid.on('change', () => {
                        this.dashboardManager.setUnsavedChanges(true);
                        this.fixResizeHandles();
                    });

                    grid.on('dragstop', () => {
                        this.dashboardManager.setUnsavedChanges(true);
                        this.fixResizeHandles();
                    });

                    grid.on('resizestop', () => {
                        this.dashboardManager.setUnsavedChanges(true);
                        this.fixResizeHandles();
                    });

                    grid.on('added', () => {
                        setTimeout(() => this.fixResizeHandles(), 100);
                    });

                    grid.on('removed', () => {
                        setTimeout(() => this.fixResizeHandles(), 100);
                    });
                }

                fixResizeHandles() {
                    if (!this.dashboardManager.grid) return;
                    try {
                        const items = this.dashboardManager.grid.getGridItems();
                        items.forEach((item) => {
                            item.classList.remove('ui-resizable-autohide');
                        });
                    } catch (error) {}
                }

                handleGridError(error) {
                    if (this.skeletonElement) {
                        this.skeletonElement.innerHTML =
                            '<p style="color: red; text-align: center; grid-column: 1 / -1; padding: 20px;">' +
                            window.dashboardTranslations.gridstackError + '</p>';
                        this.skeletonElement.style.display = 'grid';
                    }
                    if (this.gridElement) {
                        this.gridElement.style.display = 'none';
                    }
                }
            }

            class LivewireHandler {
                constructor(dashboardManager) {
                    this.dashboardManager = dashboardManager;
                }

                setupEventListeners() {
                    Livewire.on('gridstack:init', () => {
                        setTimeout(() => {
                            this.dashboardManager.gridstackHandler.initialize();
                            setTimeout(() => {
                                this.dashboardManager.gridstackHandler.fixResizeHandles();
                            }, 150);
                        }, 100);
                    });

                    Livewire.on('layoutSaved', () => {
                        this.dashboardManager.setUnsavedChanges(false);
                        setTimeout(() => {
                            this.dashboardManager.gridstackHandler.fixResizeHandles();
                        }, 200);
                    });

                    Livewire.on('js:sendLayoutForSaving', () => {
                        this.handleLayoutSaving();
                    });

                    Livewire.on('js:validationErrors', (event) => {
                        this.handleValidationErrors(event[0].errors);
                    });

                    Livewire.on('widgetAddedUnsaved', () => this.dashboardManager.setUnsavedChanges(true));
                    Livewire.on('widgetRemovedUnsaved', () => this.dashboardManager.setUnsavedChanges(true));
                    Livewire.on('widgetUpdatedUnsaved', () => this.dashboardManager.setUnsavedChanges(true));

                    const fixHandles = () => {
                        setTimeout(() => this.dashboardManager.gridstackHandler.fixResizeHandles(), 200);
                    };
                    Livewire.on('widgetAdded', fixHandles);
                    Livewire.on('widgetRemoved', fixHandles);
                    Livewire.on('refreshed', fixHandles);
                    Livewire.on('rendered', fixHandles);

                    this.setupLoadingSkeletonHandler();
                }

                setupLoadingSkeletonHandler() {
                    Livewire.hook('message.sent', (message, component) => {
                        this.updateLoadingSkeleton();
                    });

                    Livewire.hook('message.processed', (message, component) => {
                        setTimeout(() => {
                            const overlay = document.getElementById('loadingSkeletonOverlay');
                            if (overlay && !document.querySelector('[wire\\:loading]')) {
                                overlay.classList.remove('show');
                            }
                        }, 100);
                    });
                }

                updateLoadingSkeleton() {
                    const overlay = document.getElementById('loadingSkeletonOverlay');
                    if (!overlay) return;

                    const gridItems = this.dashboardManager.grid?.getGridItems() || [];
                    const itemCount = gridItems.length || 6;

                    overlay.innerHTML = '';

                    for (let i = 0; i < itemCount; i++) {
                        const skeletonItem = document.createElement('div');
                        skeletonItem.className = 'loading-skeleton-item';
                        skeletonItem.innerHTML = `
                            <div class="loading-skeleton-header">
                                <div class="loading-skeleton-title"></div>
                            </div>
                            <div class="loading-skeleton-body">
                                <div class="loading-skeleton-content"></div>
                            </div>
                        `;
                        overlay.appendChild(skeletonItem);
                    }
                }

                handleLayoutSaving() {
                    setTimeout(() => {
                        const currentLayout = this.dashboardManager.getCurrentLayout();
                        if (currentLayout.length > 0 || document.querySelector('.grid-stack')) {
                            Livewire.dispatch('persistClientLayout', [{ clientLayout: currentLayout }]);
                        } else {
                            Livewire.dispatch('persistClientLayout', [{ clientLayout: [] }]);
                        }
                    }, 100);
                }

                handleValidationErrors(errors) {
                    let errorMessages = window.dashboardTranslations.correctErrors + '\n';
                    for (const fieldKey in errors) {
                        const friendlyFieldName = this.formatFieldName(fieldKey);
                        errorMessages += `- ${friendlyFieldName}: ${errors[fieldKey].join(', ')}\n`;
                    }
                    alert(errorMessages);
                }

                formatFieldName(fieldKey) {
                    const translations = {
                        'title': window.dashboardTranslations.titleLabel,
                        'diagram_type': window.dashboardTranslations.diagramTypeLabel,
                        'pool_type': window.dashboardTranslations.poolTypeLabel
                    };

                    const cleanKey = fieldKey
                        .replace('newWidgetData.', '')
                        .replace('updatedWidgetData.', '');

                    return translations[cleanKey] || cleanKey.charAt(0).toUpperCase() + cleanKey.slice(1).replace('_', ' ');
                }
            }

            document.addEventListener('livewire:init', () => {
                window.dashboardManager = new DashboardManager();
                window.confirmRemoveWidget = (widgetId) => window.dashboardManager.confirmRemoveWidget(widgetId);
                window.reloadWidgetSettingsWithoutConfirmation = (widgetId) => window.dashboardManager.reloadWidgetSettingsWithoutConfirmation(widgetId);
                window.grid = window.dashboardManager.grid;

                document.addEventListener('livewire:navigated', () => {
                    setTimeout(() => {
                        window.dashboardManager.gridstackHandler.initialize();
                    }, 100);
                });
            });

            function createSearchableSelect(selectElement) {
                const parent = selectElement.parentElement;
                const existingContainer = parent.querySelector('.custom-select-container');
                if(existingContainer) {
                    parent.appendChild(selectElement);
                    existingContainer.remove();
                }

                const wrapper = document.createElement('div');
                wrapper.className = 'custom-select-container';
                selectElement.parentNode.insertBefore(wrapper, selectElement);
                wrapper.appendChild(selectElement);

                const selectedIndex = selectElement.selectedIndex >= 0 ? selectElement.selectedIndex : 0;
                const selectSelected = document.createElement('div');
                selectSelected.className = 'select-selected';
                selectSelected.innerHTML = selectElement.options[selectedIndex].innerHTML;
                wrapper.appendChild(selectSelected);

                const selectItems = document.createElement('div');
                selectItems.className = 'select-items';
                const searchInput = document.createElement('input');
                searchInput.type = 'text';
                searchInput.className = 'select-search';
                searchInput.placeholder = 'Search...';
                selectItems.appendChild(searchInput);
                const optionsContainer = document.createElement('div');
                selectItems.appendChild(optionsContainer);

                for (let i = 0; i < selectElement.options.length; i++) {
                    if(selectElement.options[i].disabled) continue;

                    const optionDiv = document.createElement('div');
                    optionDiv.className = 'select-item';
                    optionDiv.innerHTML = selectElement.options[i].innerHTML;
                    optionDiv.setAttribute('data-value', selectElement.options[i].value);

                    optionDiv.addEventListener('click', function() {
                        selectElement.value = this.getAttribute('data-value');
                        selectElement.dispatchEvent(new Event('change'));
                        selectSelected.innerHTML = this.innerHTML;
                        const allItems = optionsContainer.querySelectorAll('.select-item');
                        allItems.forEach(item => item.classList.remove('same-as-selected'));
                        this.classList.add('same-as-selected');
                        closeDropdown();
                    });
                    optionsContainer.appendChild(optionDiv);
                }
                wrapper.appendChild(selectItems);
                selectSelected.addEventListener('click', function(e) {
                    e.stopPropagation();
                    const isOpen = selectItems.style.display === 'block';
                    closeAllDropdowns();
                    if (!isOpen) {
                        selectItems.style.display = 'block';
                        selectSelected.classList.add('select-arrow-active');
                        searchInput.focus();
                        searchInput.value = '';
                        filterOptions('');
                    } else {
                        closeDropdown();
                    }
                });
                searchInput.addEventListener('input', function() { filterOptions(this.value); });
                searchInput.addEventListener('click', function(e) { e.stopPropagation(); });

                function filterOptions(searchTerm) {
                    const items = optionsContainer.querySelectorAll('.select-item');
                    let visibleCount = 0;
                    items.forEach(item => {
                        if (item.textContent.toLowerCase().includes(searchTerm.toLowerCase())) {
                            item.classList.remove('select-hide');
                            visibleCount++;
                        } else {
                            item.classList.add('select-hide');
                        }
                    });
                    let noResults = optionsContainer.querySelector('.no-results');
                    if (visibleCount === 0) {
                        if (!noResults) {
                            noResults = document.createElement('div');
                            noResults.className = 'no-results';
                            noResults.textContent = 'No results found';
                            optionsContainer.appendChild(noResults);
                        }
                    } else if (noResults) {
                        noResults.remove();
                    }
                }
                function closeDropdown() {
                    selectItems.style.display = 'none';
                    selectSelected.classList.remove('select-arrow-active');
                }
                function closeAllDropdowns() {
                    document.querySelectorAll('.select-items').forEach(item => { item.style.display = 'none'; });
                    document.querySelectorAll('.select-selected').forEach(item => { item.classList.remove('select-arrow-active'); });
                }
                document.addEventListener('click', closeAllDropdowns);
            }

            document.getElementById('toggleAddWidgetFormBtn').addEventListener('click', function() {
                const selectElement = document.getElementById('new_widget_pool_type');
                if (selectElement && selectElement.dataset.searchable !== 'true') {
                    createSearchableSelect(selectElement);
                    selectElement.dataset.searchable = 'true';
                }
            });
        </script>
        @endsection
