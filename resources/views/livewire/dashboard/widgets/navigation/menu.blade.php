<div>
    <link href="{{ asset('css/dashboard/right-panel.css') }}" rel="stylesheet">
    <div class="navigation-buttons">
        @if(($farbklang && $auth->user_type == \App\Enums\UserRoll::Therapist) || !$farbklang)
        <div class="livewire-menu-container" style="position: static; display: block;">
            <a class="dropdown-item livewire-menu-item @if($farbklang) dashActive_btn @endif @if($activeTab == \App\Enums\MenuTab::SYSTEM_SETTINGS->value) active @endif"
            href="javascript:void(0)" wire:click="switchTab(1)">
            <i class="ion ion-md-settings ">&nbsp;</i><span>{{trans('action.system_setting')}}</span>
            <span wire:loading wire:target="switchTab(1)" class="spinner-border spinner-border-sm ml-2" role="status"></span>
            </a>
            <a class="dropdown-item livewire-menu-item @if($activeTab == \App\Enums\MenuTab::TREATMENT_LIST->value) active @endif"
            href="javascript:void(0)" wire:click="switchTab(3)">
            <i class="ion ion-md-cart">&nbsp;</i><span>{{trans('action.last_treatment')}}</span>
            <span wire:loading wire:target="switchTab(3)" class="spinner-border spinner-border-sm ml-2" role="status"></span>
            </a>

            @if($auth->user_type == \App\Enums\UserRoll::Therapist || $auth->user_type == \App\Enums\UserRoll::Staff)
            <a class="dropdown-item livewire-menu-item @if($activeTab == \App\Enums\MenuTab::PDF_LIST->value) active @endif"
            href="javascript:void(0)" wire:click="switchTab(2)">
            <i class="fas fa-file-pdf">&nbsp;</i><span>{{trans('action.pdf_heading')}}</span>
            <span wire:loading wire:target="switchTab(2)" class="spinner-border spinner-border-sm ml-2" role="status"></span>
            </a>
            <a class="dropdown-item livewire-menu-item @if($activeTab == \App\Enums\MenuTab::DC_PDF_LIST->value) active @endif"
            href="javascript:void(0)" wire:click="switchTab(5)">
            <i class="fas fa-file-pdf">&nbsp;</i><span>{{trans('action.digitalcon&focusPDF')}}</span>
            <span wire:loading wire:target="switchTab(5)" class="spinner-border spinner-border-sm ml-2" role="status"></span>
            </a>
            @endif
            @if ($auth->user_type == \App\Enums\UserRoll::Admin)
            <a class="dropdown-item livewire-menu-item @if($activeTab == \App\Enums\MenuTab::USERS->value) active @endif"
            href="javascript:void(0)" wire:click="switchTab(4)">
            <i class="ion ion-ios-contacts">&nbsp;</i><span>{{trans('action.user')}}</span>
            <span wire:loading wire:target="switchTab(4)" class="spinner-border spinner-border-sm ml-2" role="status"></span>
            </a>
            @endif
        </div>
        @endif
        @if($farbklang && $auth->user_type == \App\Enums\UserRoll::Admin)
        <button type="button" class="tabs-itemBox-Style" id="users_button"><i
                class="fas fa-users">&nbsp;</i><span>{{trans('action.user')}}</span></button>
        @endif
    </div>
    <div class="tab-contents">
        <div id="tab-1" class="tab-content @if($activeTab != \App\Enums\MenuTab::SYSTEM_SETTINGS->value) hide @endif">
            <div class="card">
                <div class="arrow"></div>
                <div class="card-body web-kit-scroll">
                    <ul class="sys-setting">
                        <li>
                            <p>
                                {{trans('action.calculation_system_dashboard')}}
                            </p>
                            <div class="right-side">
                                <div class="d-flex align-items-center">
                                    <div class="flex-shrink-1 mr-2">{{trans('action.year')}}</div>
                                    <label class="switcher switcher-lg switcher-success m-0">
                                        <input type="checkbox" onclick="changeYearMonth()" id="changeYearMonth"
                                            @if($userDetails->calculation_with == 1) {{'checked'}} @endif
                                        class="switcher-input">
                                        <span class="switcher-indicator">
                                            <span class="switcher-yes"></span>
                                            <span class="switcher-no"></span>
                                        </span>
                                    </label>
                                    <div class="flex-shrink-1 text-success ml-2">{{trans('action.month')}}</div>
                                </div>
                            </div>
                        </li>
                        <li>
                            <p>
                                {{trans('action.date_system_dashboard')}}
                            </p>
    
                            <div class="right-side">
                                <div class="right-side ml-3 ml-sm-0">
                                    <input type="text" class="form-control datef" data-date="{{ $userDetails->datumcore }}"
                                        id="datePicker" onchange="dateChange()" placeholder="mm/dd/yyyy"
                                        value="{{ \Carbon\Carbon::parse($userDetails->datumcore)->toDateString() }}">
                                </div>
                            </div>
                        </li>
                        <li>
                            <p>
                                {{trans('action.sorting_system_dashboard')}}
                            </p>
                            <div class="right-side">
                                <select class="custom-select" id="changeShowFilter" onchange="changeShowFilter()">
                                    <option @if ($data['filterid']==1) {{ 'selected' }} @endif value="1">A-Z</option>
                                    <option @if ($data['filterid']==2) {{ 'selected' }} @endif value="2">Z-A</option>
                                    <option @if ($data['filterid']==3) {{ 'selected' }} @endif value="3">1-100</option>
                                    <option @if ($data['filterid']==4) {{ 'selected' }} @endif value="4">100-1</option>
                                </select>
                            </div>
                        </li>
                    </ul>
    
                </div>
            </div>
        </div>
    
        <div id="tab-2" class="tab-content @if($activeTab != \App\Enums\MenuTab::PDF_LIST->value) hide @endif">
            <div class="card">
                <div class="arrow"></div>
                <div class="card-body" id="show_normal_pdfs">
                    @if($loadingTab == 2)
                        <div class="preloader" style="min-height:270px"><img style="top:20px !important" src="/images/Fill-4.png" alt=""></div>
                    @elseif($errorMessage && $activeTab == \App\Enums\MenuTab::PDF_LIST->value)
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle"></i> {{ $errorMessage }}
                            <button type="button" class="btn btn-sm btn-outline-danger ml-2" wire:click="retryLoadData(2)">
                                <i class="fas fa-redo"></i> Retry
                            </button>
                        </div>
                    @elseif($activeTab == \App\Enums\MenuTab::PDF_LIST->value && $tabDataLoaded[2])
                        @include('Frontend.Section.normal_pdf_view', ['data' => $pdfList])
                    @endif
                </div>
            </div>
        </div>
    
        <div id="tab-3" class="tab-content @if($activeTab != \App\Enums\MenuTab::TREATMENT_LIST->value) hide @endif">
            <div class="card">
                <div class="arrow"></div>
                <div class="card-body" id="save_treatment_cart_list" style="padding: 15px 10px 15px 15px;">
                    @if($loadingTab == 3)
                        <div class="preloader" style="min-height:270px"><img style="top:20px !important" src="/images/Fill-4.png" alt=""></div>
                    @elseif($errorMessage && $activeTab == \App\Enums\MenuTab::TREATMENT_LIST->value)
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle"></i> {{ $errorMessage }}
                            <button type="button" class="btn btn-sm btn-outline-danger ml-2" wire:click="retryLoadData(3)">
                                <i class="fas fa-redo"></i> Retry
                            </button>
                        </div>
                    @elseif($activeTab == \App\Enums\MenuTab::TREATMENT_LIST->value && $tabDataLoaded[3])
                        @include('Frontend.Section.treatment_list', ['data' => $treatmentList])
                    @endif
                </div>
            </div>
        </div>
    
        <div id="tab-4" class="tab-content @if($activeTab != \App\Enums\MenuTab::USERS->value) hide @endif">
            @if ($auth->user_type == \App\Enums\UserRoll::Admin)
            <div class="card">
                <div class="@if($farbklang) arrow arrow3 @else arrow @endif"></div>
                <div class="card-body">
                    <div class="row">
                        {{-- user --}}
                        <div class="col-md-12">
                            <ul class="users-rankwise">
                                <li>
                                    <div class="user-single-box text-center">
                                        <div class="usb-photo">
                                            @if($userDetails->id !== $auth->id)
                                            <img data-name="{{$auth->fullName}}" data-email="{{$auth->email}}"
                                                data-view="true" src="{{ getProfileImage($auth) }}" id="upimg">
                                            @else
                                            <img data-name="{{$userDetails->fullName}}" data-email="{{$userDetails->email}}"
                                                data-view="true" data-profileimage="true" id="upimg">
                                            @endif
                                        </div>
                                        <div class="usb-content">
                                            <p>{{ $admin->fullName}}</p>
                                            <span class="text-primary">{{trans('action.no_access2')}}</span>
                                        </div>
                                    </div>
                                </li>
    
                                @if($subusers != null)
                                @foreach ($subusers as $user)
                                <li>
                                    <div class="user-single-box text-center">
                                        <div class="usb-photo" onclick="switch_user({{$user->id}})">
                                            {{-- @if($user->photo == "")
                                            <img class="rounded-circle m-1 p-1"
                                                data-src="{!! ($user->gender == 2)? asset('/images/female.jpg') : asset('/images/avatar.png') !!}"
                                                alt="{{ucfirst(substr($user->first_name,0,1)).' '.ucfirst(substr($user->last_name,0,1)) }}" />
                                            @else
                                            <img class="rounded-circle m-1 p-1"
                                                data-src="{{ getEmbededImage('/profile/users/'.$user->photo) }}"
                                                alt="{{ucfirst(substr($user->first_name,0,1)).' '.ucfirst(substr($user->last_name,0,1)) }}" />
                                            @endif --}}
                                            <img class="rounded-circle m-1 p-1" data-src="{{getProfileImage($user)}}"
                                                alt="{{ucfirst(substr($user->first_name,0,1)).' '.ucfirst(substr($user->last_name,0,1)) }}" />
                                        </div>
                                        <button type="button" class="minus-btn" onclick="deleteSubUser({{$user->id}})"
                                            data-toggle="tooltip" data-placement="bottom" data-state="secondary"
                                            title="{{ trans('action.delete')}} {{$user->first_name}}&nbsp;{{ $user->last_name }}"><i
                                                class="fa text-danger fa-minus-circle"></i></button>
    
                                        <div class="usb-content">
                                            <p>{{$user->fullName }}</p>
                                        </div>
                                    </div>
                                </li>
                                @endforeach
                                @endif
    
                                {{-- creat user --}}
                                @if($addUserStatus)
                                <li>
                                    <div class="user-single-box text-center">
                                        <a href="{{ route('users.show') }}">
                                            <div class="usb-icon">
                                                <i class="fa fa-plus" aria-hidden="true"></i>
                                            </div>
                                        </a>
                                        <div class="usb-content">
                                            <p>{{trans('action.create_new_user')}}</p>
                                        </div>
                                    </div>
                                </li>
                                @endif
                            </ul>
                        </div>
    
                    </div>
                </div>
            </div>
            @endif
        </div>
    
        <div id="tab-5" class="tab-content @if($activeTab != \App\Enums\MenuTab::DC_PDF_LIST->value) hide @endif">
            @if ($auth->user_type == \App\Enums\UserRoll::Therapist || $auth->user_type == \App\Enums\UserRoll::Staff)
            <div class="card ">
                <div class="arrow"></div>
                <div class="card-body" id="dc_pdf_view_show">
                    @if($loadingTab == 5)
                        <div class="preloader" style="min-height:270px"><img style="top:20px !important" src="/images/Fill-4.png" alt=""></div>
                    @elseif($errorMessage && $activeTab == \App\Enums\MenuTab::DC_PDF_LIST->value)
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle"></i> {{ $errorMessage }}
                            <button type="button" class="btn btn-sm btn-outline-danger ml-2" wire:click="retryLoadData(5)">
                                <i class="fas fa-redo"></i> Retry
                            </button>
                        </div>
                    @elseif($activeTab == \App\Enums\MenuTab::DC_PDF_LIST->value && $tabDataLoaded[5])
                        @include('Frontend.Section.dc_pdf_view', ['data' => $dcPdfList])
                    @endif
                </div>
            </div>
            @endif
        </div>
    </div>
</div>
