<div>
    <canvas id="{{$poolId}}_widget_chart"></canvas>
</div>

@assets
<script src="{{ asset('/js/chart/chart.min.js') }}"></script>
<script src="{{ asset('/js/chart/chartjs-plugin-annotation.min.js') }}"></script>
@endassets

@script
<script>
    let id = '{{$poolId}}';
    let results = @json($results['results']);
    let labels = @json($results['labels']);
    let filter = '{{$results['filter']}}';

    function createLineChart(id, labels, datasets, filters) {
        const options = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: { display: false },
                zoom: {
                    pan: { enabled: true, mode: 'xy' },
                    zoom: { enabled: true, mode: 'xy' },
                },
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: { stepSize: 10 }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            },
            elements: {
                line: {
                    tension: 0.4 // Smooths the line slightly
                }
            }
        };

        const config = {
            type: 'line',
            data: {
                labels: labels,
                datasets: getFormattedDatasets(datasets, filters)
            },
            options: options
        };

        const canvasId = `${id}_widget_chart`;
        const ctx = document.getElementById(canvasId).getContext('2d');

        if (window.chartInstances && window.chartInstances[canvasId]) {
            window.chartInstances[canvasId].destroy();
        }
        window.chartInstances = { ...(window.chartInstances || {}), [canvasId]: new Chart(ctx, config) };
    }

    function getFormattedDatasets(datasets, filters) {
        if (!filters) {
            const chartColors = getChartColors(datasets);
            return [{
                data: datasets,
                backgroundColor: 'rgba(47, 171, 102, 0.1)', // Light fill under line
                borderColor: 'rgba(47, 171, 102, 1)', // Green line color
                borderWidth: 2,
                pointBackgroundColor: chartColors.backgroundColor, // Points use your color scheme
                pointBorderColor: chartColors.borderColor,
                pointRadius: 4,
                pointHoverRadius: 6,
                fill: true // Adds fill under the line
            }];
        }
        
        // If filters exist (multiple lines)
        return datasets.map((data, index) => ({
            data: data,
            backgroundColor: index === 0 ? 'rgba(255, 99, 132, 0.1)' : 'rgba(75, 192, 192, 0.1)',
            borderColor: index === 0 ? 'rgba(255, 99, 132, 1)' : 'rgba(47, 171, 102, 1)',
            borderWidth: 2,
            pointBackgroundColor: index === 0 ? 'rgba(255, 99, 132, 1)' : 'rgba(47, 171, 102, 1)',
            pointBorderColor: '#fff',
            pointRadius: 4,
            pointHoverRadius: 6,
            fill: true
        }));
    }

    function getChartColors(dataSet) {
        // Add null safety check
        if (!dataSet || !Array.isArray(dataSet)) {
            return { backgroundColor: [], borderColor: [] };
        }

        const getColor = value => {
            if (value <= 10) return ['rgba(232, 78, 27, 1)', 'rgb(232, 78, 27)'];
            if (value <= 69) return ['rgba(248, 177, 51, 1)', 'rgb(248, 177, 51)'];
            if (value <= 100) return ['rgba(47, 171, 102, 1)', 'rgb(47, 171, 102)'];
            return ['rgba(0, 0, 0, 0.2)', 'rgb(0, 0, 0)'];
        };

        return dataSet.reduce((colors, value) => {
            const [bgColor, borderColor] = getColor(value);
            colors.backgroundColor.push(bgColor);
            colors.borderColor.push(borderColor);
            return colors;
        }, { backgroundColor: [], borderColor: [] });
    }

    createLineChart(id, labels, results, filter);

    // Handle window resize to maintain chart responsiveness
    const optimizedResize = debounce(() => {
        createLineChart(id, labels, results, filter);
    }, 200);

    window.addEventListener('resize', optimizedResize);

    function debounce(func, wait) {
        let timeout;
        return function (...args) {
            const context = this;
            clearTimeout(timeout);
            timeout = setTimeout(() => func.apply(context, args), wait);
        };
    }
</script>
@endscript