<div>
   
    @if(!$selectedLongDay)
        <div class="square-progress-container dashboard-progress-bar-container">
            @foreach ($results['results'] as $key => $item)
                <div wire:key="dashboard-progress-container-{{ $key }}-{{ $item['analysis_id'] }}"
                    class="dashboard-progress-container progress-{{ $key }} d-flex justify-content-center align-items-center pb-2">
                    <div class="progress-item">
                        <div class="d-flex flex-shrink-0 align-items-center justify-content-between">
                            <p class="text-truncate mb-0" style="width: 125px;" data-toggle="tooltip" data-placement="left" data-original-title="{{ $item['name'] }}">{{ $item['name'] }}</p>
                            <div class="action-btn btn-1">
                                @if(!empty($item['description']) || !empty($item['desc_img']) || !empty($item['bodyDesc']) || !empty($item['mentalDesc']))
                                    <span class="d-block info popover-trigger" 
                                        data-poolid="{{ $poolId }}"
                                        data-analysisid="{{ $item['analysis_id'] }}"
                                        data-key="{{ $key }}"
                                        data-container="body"
                                        data-toggle="popover"
                                        data-trigger="click"
                                        data-placement="bottom"
                                        data-html="true"
                                        data-content="">
                                        <i class="fas fa-info-circle"></i>
                                    </span>
                                @endif
                                <div style="transform: translateY(-4px); margin-left: 5px">
                                    <livewire:dashboard.single-analyses-add-to-cart
                                        :wire:key="'dashboard-progress-btn1-'.$key . '-' .$item['analysis_id'] . '-' . $poolId"
                                        :poolId="$poolId"
                                        :analysesId="$item['analysis_id']"/>
                                </div>
                            </div>
                        </div>
                        <div class="d-flex align-items-center" style="gap: 10px;width: 100%">
                            <div class="action-btn btn-2">
                                @if(!empty($item['description']) || !empty($item['desc_img']) || !empty($item['bodyDesc']) || !empty($item['mentalDesc']))
                                    <span class="d-block info popover-trigger"
                                        data-poolid="{{ $poolId }}"
                                        data-analysisid="{{ $item['analysis_id'] }}"
                                        data-key="{{ $key }}"
                                        data-container="body"
                                        data-toggle="popover"
                                        data-trigger="click"
                                        data-placement="bottom"
                                        data-html="true"
                                        data-content="">
                                        <i class="fas fa-info-circle"></i>
                                    </span>
                                @endif
                                <div style="transform: translateY(-4px); margin-left: 5px">
                                    <livewire:dashboard.single-analyses-add-to-cart
                                        :wire:key="'dashboard-progress-btn2-'.$key . '-' .$item['analysis_id'] . '-' . $poolId"
                                        :poolId="$poolId"
                                        :analysesId="$item['analysis_id']"/>
                                </div>
                            </div>
                            <svg width="100%" height="18">
                                <!-- Background Rectangle -->
                                <rect x="0" y="0" width="100%" height="18" fill="#e0e0e0" rx="5" ry="5"></rect>
                                <!-- Progress Rectangle -->
                                <rect x="0" y="0" width="{{ $item['val'] }}%" height="18" fill="{{ $item['color'] }}" rx="5" ry="5">
                                </rect>
                                <text x="50%" y="55%"
                                    style="font-size: 12px; font-family: Arial, sans-serif; dominant-baseline: middle; text-anchor: middle; fill: #ffffff;">{{
                            $item['val'] }}%</text>
                            </svg>
                        </div>
                    </div>
                </div>
            
                <!-- Hidden popover content -->
                <div id="popover-content-{{ $poolId }}-{{ $item['analysis_id'] }}" class="d-none">
                    @if(!empty($item['description']))
                        <h6>{{__('action.main_desc')}}</h6>
                        <p>{!! $item['description'] !!}</p>
                    @endif
                    @if(isset($item['desc_img']))
                        <img loading="lazy" src="{{ asset('/storage/analyse/images/description') }}/{{ $item['desc_img'] }}" class="popover-showimg" id="popover-img{{ $item['analysis_id'] }}-desc" data-type="desc" data-id="{{ $item['analysis_id'] }}" alt="" height="250px" width="auto">
                    @endif
                    <hr>
                    @if(!empty($item['bodyDesc']))
                        <h6>{{__('action.body')}}</h6>
                        <p>{!! $item['bodyDesc'] !!}</p>
                    @endif
                    <hr>
                    @if(!empty($item['mentalDesc']))
                        <h6>{{__('action.mental')}}</h6>
                        <p>{!! $item['mentalDesc'] !!}</p>
                    @endif
                </div>
            
            @endforeach
            
            <!-- Modal for image popup -->
            <div id="analysisPopup_Modal" class="modal analysisPopup_Modal">
                <span class="close" id="modal-close">&times;</span>
                <img class="modal-content" id="imgShow">
            </div>
        </div>
    @else 
    @dump($results['results']);
        <div class="dashboard-progress-bar-container">
            {{-- @foreach($results as $key => $item) --}}
              
                {{-- @php
                    $redWidth = ($item['RED'] / ($item['RED'] + $item['GREEN'])) * 100;
                    $greenWidth = 100; // since green starts where red ends
                @endphp
                <div wire:key="dashboard-long-day-progress-container-{{ $key }}" style="margin-bottom: 20px; position: relative;">
                    <div class="col-lg-3 col-md-3" style="max-width: 100%; margin-bottom: 10px;display:flex;">
                        <p style="margin: 4px;display: inline-block;">{{ $item['name'] }}</p>
                        <livewire:dashboard.single-analyses-add-to-cart
                            :wire:key="'dashboard-long-day-progress-container-'.$key .'-'. $item['analysis_id']" :poolId="$poolId"
                            :analysesId="$item['analysis_id']">
                    </div>
                    <div style="position: relative; height: 25px; background-color: #f3f3f3; border-radius: 5px; overflow: hidden;">
                        <div
                            style="position: absolute; height: 100%; top: 0; border-radius: 5px; background-color: #E84E1B; z-index: 1; width: {{ $redWidth }}%;border-top-right-radius: 0;border-bottom-right-radius: 0;">
                        </div>
                        <div
                            style="position: absolute; height: 100%; top: 0; border-radius: 5px; background-color: #2FAB66; z-index: 2; width: {{ $greenWidth }}%; left: {{ $redWidth }}%; border-top-left-radius: 0;border-bottom-left-radius: 0;">
                        </div>
                    </div>
                    <div class="analysis-count"
                        style="position: absolute; width: 100%; text-align: center;top: 45px !important; height: 100%; display: flex; align-items: center; justify-content: center;">
                        <span style="color: #E84E1B" data-toggle="tooltip" data-placement="bottom" title="Rote Werte">{{
                            $item['RED'] }}</span>
                        <span>⁄</span>
                        <span style="color: #2FAB66" data-toggle="tooltip" data-placement="bottom" title="Grüne Werte">{{
                            $item['GREEN'] }}</span>
                    </div>
                </div> --}}
            {{-- @endforeach --}}
        </div>
    @endif

    <style>
        .dashboard-progress-container .progress-item {
            display: flex;
            align-items: center;
            width: 100%;
        }

        .dashboard-progress-container .action-btn {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .dashboard-progress-container .action-btn.btn-1{
            display: none;
        }

        .info {
            cursor: pointer;
        }

        @media (min-width: 992px) and (max-width: 1200px) {

            .dashboard-progress-container .progress-item {
                flex-direction: column;
                justify-content: center;
                align-items: start;
            }

            .dashboard-progress-container .progress-item div:first-child {
                width: 100%;
            }

            .dashboard-progress-container .progress-item .btn-1 {
                display: flex !important;
            }

            .dashboard-progress-container .progress-item .btn-2 {
                display: none !important;;
            }
        }
    </style>
</div>

@script
<script>
    const initTooltips = () => {
        $('[data-toggle="tooltip"]').tooltip({ container: 'body' });
    };

    const initPopovers = () => {
        $('[data-toggle="popover"]').each(function () {
            const $el = $(this);
            $el.popover('dispose').popover({
                html: true,
                trigger: 'manual',
                placement: 'bottom',
                container: 'body',
                content: function () {
                    const id = `#popover-content-${$el.data('poolid')}-${$el.data('analysisid')}`;
                    return $(id).html();
                }
            });

            $el.off('click').on('click', function (e) {
                e.stopPropagation();
                $('[data-toggle="popover"]').not(this).popover('hide');
                $el.popover('toggle');
            });
        });

        // Hide popovers when clicking outside
        $(document).off('click.popover-close').on('click.popover-close', function (e) {
            if (!$(e.target).closest('.popover, [data-toggle="popover"]').length) {
                $('[data-toggle="popover"]').popover('hide');
            }
        });
    };

    const initImageModal = () => {
        $(document).off('click.image-modal').on('click.image-modal', '.popover-showimg', function () {
            $('#imgShow').attr('src', this.src);
            $('#analysisPopup_Modal').fadeIn();
        });

        $('#modal-close, #analysisPopup_Modal').off('click.image-modal').on('click.image-modal', function (e) {
            if (e.target.id === 'modal-close' || e.target.id === 'analysisPopup_Modal') {
                $('#analysisPopup_Modal').fadeOut();
            }
        });
    };

    const initAll = () => {
        initTooltips();
        initPopovers();
    };

    document.addEventListener('DOMContentLoaded', () => {
        initAll();
        initImageModal();
    });

    document.addEventListener('livewire:navigated', () => setTimeout(initAll, 100));
    document.addEventListener('livewire:updated', () => setTimeout(initAll, 100));
    $wire.on('$refresh', () => setTimeout(initAll, 100));
</script>
@endscript