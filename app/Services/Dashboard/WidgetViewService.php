<?php

namespace App\Services\Dashboard;

use App\Model\User;
use App\Statics\Calculation;
use App\Model\Dashboards\Dashboard;
use App\Traits\PoolAnalysesGeneralFunctions;
use App\Model\Dashboards\ModuleHeadDashboard;

class WidgetViewService
{
    use PoolAnalysesGeneralFunctions;

    public function processWidgetView($poolId,$diagramType,$filters = [], $customSorting = []){

        $userId = getUserDetails()->id;
        $user = User::with('userfilter:user_id,filter_type')->find($userId, [
                    'id',
                    'language_id',
                    'first_name',
                    'last_name',
                    'gebort',
                    'gebdatum',
                    'datumcore',
                    'calculation_with',
                    'thema_speichern'
                ]);
        $sorting =  $user->userfilter ? $user->userfilter->filter_type : 3;
        $langBaseAnalyses = $this->poolIdToUserLngBaseAnalysesGet($poolId);
        #need all time de formated analyses name
        $deAnalysesForCalculation = $langBaseAnalyses->pluck(app()->getLocale() == 'de' ? 'name' : 'de_name', 'id')->toArray() ?? [];
        #According to the user's language 
        $targetedLangbaseAnalysesName = [
            'targetedAnalysesName' => $langBaseAnalyses->pluck('name', 'id')->toArray() ?? [],
            'description'          => $langBaseAnalyses->pluck('description', 'id')->toArray() ?? [],
            'desc_img'             => $langBaseAnalyses->pluck('desc_image', 'id')->toArray() ?? [],
            'body_desc'            => $langBaseAnalyses->pluck('body_desc', 'id')->toArray() ?? [],
            'mental_desc'          => $langBaseAnalyses->pluck('mental_desc', 'id')->toArray() ?? [],
        ];

        #bussness logic
        $analysisCalculationResponse = $this->executeAnalysisCalculation($poolId);

          #bussness logic
        if($filters['longDay']){
            $longDayAnalyses = [];
            $start_date = $user->datumcore;
            for ($i = 0; $i < $filters['longDay']; $i++) {
                $curdate = date('Y-m-d', strtotime($start_date . '+' . $i . ' days'));
                Calculation::setDatumcore($curdate);
                $longDayAnalyses[] = $this->executeAnalysisCalculation($poolId);
            }
            $analysisCalculationResponse = $this->analysesColorCountAlgorithm($longDayAnalyses);
        }else{
            $analysisCalculationResponse = $this->executeAnalysisCalculation($poolId);
        }

        $sortResult = $this->processAndSortLangBaseData($targetedLangbaseAnalysesName, $deAnalysesForCalculation, $analysisCalculationResponse);
        $sortResult = $this->filterAndSortArray($sortResult, $sorting);

        if($customSorting) {
            $sortResult = $this->sortArrayByAnalysisIdOrder($sortResult, $customSorting);
        }
        // Format the sorted result as expected for final response
        return $this->diagramTypeBaseFormat($sortResult, $diagramType, $filters['longDay'] ?? null);
    }

    private function sortArrayByAnalysisIdOrder(array $data, array $sorting)
    {
        // Create a map for quick lookup of the order index of each analysis_id
        $orderMap = array_flip($sorting); // analysis_id => position

        usort($data, function ($a, $b) use ($orderMap) {
            $posA = $orderMap[$a['analysis_id']] ?? PHP_INT_MAX; 
            $posB = $orderMap[$b['analysis_id']] ?? PHP_INT_MAX;
            return $posA <=> $posB;
        });

        return $data;
    }
}