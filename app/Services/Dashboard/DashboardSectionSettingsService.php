<?php

namespace App\Services\Dashboard;

use App\Model\Dashboards\BaseDashboardModal;
use App\Model\Dashboards\ModuleHeadDashboard;
use App\Model\Dashboards\V2Dashboard;
use App\Model\User;
use App\Statics\Calculation;
use App\Traits\PoolAnalysesGeneralFunctions;

class DashboardSectionSettingsService
{
    use PoolAnalysesGeneralFunctions;

    public function getModelInstance(string $model): BaseDashboardModal
    {
        switch ($model) {
            case 'ModuleHeadDashboard':
                return new ModuleHeadDashboard();
            default:
                return new V2Dashboard();
        }
    }

    public function processDashboardDiagramSection(string $modelClass,int $id, array $filters){

        $dashboardSectionSetting = $this->getModelInstance($modelClass)
                ->select('id', 'diagram_type', 'pool_id', 'sorted_diagram','is_override_sorting')
                ->where('id', $id)
                ->first();
        $isOverrideSorting = $dashboardSectionSetting->is_override_sorting ?? false;
        $userId = getUserDetails()->id;
        $user = User::with('userfilter:user_id,filter_type')->find($userId, [
                'id',
                'language_id',
                'first_name',
                'last_name',
                'gebort',
                'gebdatum',
                'datumcore',
                'calculation_with',
                'thema_speichern'
            ]);
        $sorting =  $user->userfilter ? $user->userfilter->filter_type : 3;
        $langBaseAnalyses = $this->poolIdToUserLngBaseAnalysesGet($dashboardSectionSetting->pool_id);
        #need all time de formated analyses name
        $deAnalysesForCalculation = $dashboardSectionSetting->sorted_diagram ? $dashboardSectionSetting->sorted_diagram : $langBaseAnalyses->pluck(app()->getLocale() == 'de' ? 'name' : 'de_name', 'id')->toArray() ?? [];
        #According to the user's language
        $targetedLangbaseAnalysesName = [
            'targetedAnalysesName' => $langBaseAnalyses->pluck('name','id')->toArray() ?? [],
            'description' => $langBaseAnalyses->pluck('description','id')->toArray() ?? [],
            'desc_img'=> $langBaseAnalyses->pluck('desc_image','id')->toArray() ?? [],
            'body_desc' => $langBaseAnalyses->pluck('body_desc','id')->toArray() ?? [],
            'mental_desc' => $langBaseAnalyses->pluck('mental_desc','id')->toArray() ?? []   
        ];
        #bussness logic
        if($filters['longDay']){
            $longDayAnalyses = [];
            $start_date = $user->datumcore;
            for ($i = 0; $i < $filters['longDay']; $i++) {
                $curdate = date('Y-m-d', strtotime($start_date . '+' . $i . ' days'));
                Calculation::setDatumcore($curdate);
                $longDayAnalyses[] = $this->executeAnalysisCalculation($dashboardSectionSetting->pool_id);
            }
            $analysisCalculationResponse = $this->analysesColorCountAlgorithm($longDayAnalyses);
        }else{
            $analysisCalculationResponse = $this->executeAnalysisCalculation($dashboardSectionSetting->pool_id);
        }
        $sortResult = $this->processAndSortLangBaseData($targetedLangbaseAnalysesName, $deAnalysesForCalculation, $analysisCalculationResponse);
        if (!$isOverrideSorting) {
            $sortResult = $this->filterAndSortArray($sortResult, $sorting);
        }
        // Format the sorted result as expected for final response
        return $this->diagramTypeBaseFormat($sortResult, $dashboardSectionSetting->diagram_type, $filters['longDay']);
    }
}