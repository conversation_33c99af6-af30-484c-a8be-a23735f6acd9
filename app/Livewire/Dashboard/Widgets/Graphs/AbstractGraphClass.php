<?php

namespace App\Livewire\Dashboard\Widgets\Graphs;

use App\Enums\DiagramType;
use App\Services\Dashboard\WidgetViewService;
use App\Traits\LivewireGeneralFunctions;
use App\Traits\PoolAnalysesGeneralFunctions;
use Livewire\Attributes\On;
use Livewire\Component;

abstract class AbstractGraphClass extends Component
{
    use LivewireGeneralFunctions, PoolAnalysesGeneralFunctions;

    public $poolId;
    public $settings;
    public $results;
    public $selectedLongDay;
    protected static DiagramType $diagramType;

    public function mount($poolId, $settings = [])
    {
        $this->poolId = (int)$poolId;
        $this->settings = $settings;
        $this->fetchResults();
    }

    #[On('daySelected')]
    public function handleDaySelected($day)
    {
        $this->selectedLongDay = $day;
        $this->fetchResults();
    }

    protected function fetchResults()
    {
        $widgetService = app(WidgetViewService::class);
        $sorting = $this->settings['sorting'] ?? [];

        $this->results = $widgetService->processWidgetView(
            $this->poolId,
            static::$diagramType->value,
            ['longDay' => $this->selectedLongDay],
            $sorting
        ) ?? [];
    }
}