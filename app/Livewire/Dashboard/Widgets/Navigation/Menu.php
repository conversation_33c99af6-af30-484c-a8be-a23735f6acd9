<?php

namespace App\Livewire\Dashboard\Widgets\Navigation;

use Livewire\Component;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use App\Model\User;
use App\Enums\MenuTab;

class Menu extends Component
{
    public $data;
    public $addUserStatus;
    public $auth;
    public $subusers;
    public $farbklang = false;
    public $userDetails;
    public $admin;

    // State management properties
    public $activeTab = 1;
    public $loadingTab = null;
    public $pdfList = [];
    public $treatmentList = [];
    public $dcPdfList = [];
    public $errorMessage = '';
    public $tabDataLoaded = [
        MenuTab::SYSTEM_SETTINGS->value => true,
        MenuTab::PDF_LIST->value => false,
        MenuTab::TREATMENT_LIST->value => false,
        MenuTab::USERS->value => true,
        MenuTab::DC_PDF_LIST->value => false,
    ];

    public function mount($data = [])
    {
        $this->data = $data;
        $this->getMenuInfo();
    }

    private function getMenuInfo()
    {
        $this->userDetails = getUserDetails();
        $this->addUserStatus = (maxAddUser() <= 0) ? false : true;
        $this->auth = Auth::user();
        $this->admin = $this->auth; // Admin is the same as auth user in this context
        $this->subusers = getSubUser();
        $this->data['filterid'] = !isset($this->data['filterid']) ? $this->userDetails?->userfilter()?->first(['filter_type'])?->filter_type : $this->data['filterid'];
    }

    public function switchTab($tabNumber)
    {
        $this->errorMessage = '';
        $this->activeTab = $tabNumber;

        if (
            !$this->tabDataLoaded[$tabNumber] &&
            in_array($tabNumber, [
                MenuTab::PDF_LIST->value,
                MenuTab::TREATMENT_LIST->value,
                MenuTab::DC_PDF_LIST->value
            ])
        ) {
            $this->loadTabData($tabNumber);
        }
    }

    public function loadTabData($tabNumber)
    {
        $this->loadingTab = $tabNumber;
        $this->errorMessage = '';

        try {
            switch ($tabNumber) {
                case MenuTab::PDF_LIST->value:
                    $this->loadPdfList();
                    break;
                case MenuTab::TREATMENT_LIST->value:
                    $this->loadTreatmentList();
                    break;
                case MenuTab::DC_PDF_LIST->value:
                    $this->loadDcPdfList();
                    break;
                default:
                    throw new \Exception('Invalid tab number: ' . $tabNumber);
            }

            $this->tabDataLoaded[$tabNumber] = true;
        } catch (\Exception $e) {
            // Set error message for display in the UI
            $this->errorMessage = 'Failed to load data: ' . $e->getMessage();

            // Log the error for debugging
            Log::error('Menu widget tab data loading failed', [
                'tab' => $tabNumber,
                'user_id' => getUserId(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        } finally {
            $this->loadingTab = null;
        }
    }

    private function loadPdfList()
    {
        $loginUser = User::find(getUserId() ?? Auth::id());

        if ($loginUser->user_type == 2 || $loginUser->user_type == 1) {
            $pdfs = DB::table('all_pdfs')
                ->where('creator_id', getUserId() ?? Auth::id())
                ->where('pdf_type', '!=', 'Normal')
                ->orderBy('created_at', 'DESC')
                ->limit(100)
                ->get();
        } else {
            $pdfs = DB::table('all_pdfs')
                ->where('fuser_id', getUserId() ?? Auth::id())
                ->where('pdf_type', '!=', 'Normal')
                ->orderBy('created_at', 'DESC')
                ->limit(100)
                ->get();
        }

        $this->pdfList = $pdfs->toArray();
    }

    private function loadTreatmentList()
    {
        $result = DB::table('cart_package_users')
            ->select('cart_packages.package_name', 'cart_packages.id', 'cart_packages.created_at')
            ->join('cart_packages', 'cart_package_users.cart_package_id', 'cart_packages.id')
            ->where('cart_package_users.user_id', getUserId())
            ->orderBy('cart_packages.created_at', 'DESC')
            ->limit(100)
            ->get();

        $this->treatmentList = $result->toArray();
    }

    private function loadDcPdfList()
    {
        $loginUser = User::find(getUserId() ?? Auth::id());

        if ($loginUser->boss_id == 0) {
            $dcfpdf = DB::table('all_pdfs')
                ->where('creator_id', Auth::id())
                ->where('pdf_type', 'Normal')
                ->orderBy('created_at', 'DESC')
                ->limit(100)
                ->get();
        } else {
            $dcfpdf = DB::table('all_pdfs')
                ->where('fuser_id', getUserId())
                ->where('pdf_type', 'Normal')
                ->orderBy('created_at', 'desc')
                ->limit(100)
                ->get();
        }

        $this->dcPdfList = $dcfpdf->toArray();
    }

    public function retryLoadData($tabNumber)
    {
        $this->tabDataLoaded[$tabNumber] = false;
        $this->errorMessage = '';
        $this->loadTabData($tabNumber);
    }

    public function render()
    {
        return view('livewire.dashboard.widgets.navigation.menu');
    }
}
