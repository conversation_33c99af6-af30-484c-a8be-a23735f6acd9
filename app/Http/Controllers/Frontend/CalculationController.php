<?php

namespace App\Http\Controllers\Frontend;

use App\Model\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;

class CalculationController extends Controller
{
    function getBiorhythmus(Request $req){
        $user = User::find(getUserId());
        $get_resp = app('App\Services\Calculation\CalculationService')->calcPersonForDays($user,'',($req->days -1)??7);
        return response()->json([
            'success' => true,
            'lists'=> $get_resp
        ]);
    }

    /**
     * Get Calculation based on user and analyses
     * @param [first_name, last_name, birthplace, birthdate, analyses] ,
     * @param [optional- topic, cal_for_month, cal_day ]
     * @return success,results[day,day_month,user_report,analyses_report]
     */
    function getCalculationForSingleEntries(Request $req){
        $req->validate([
            'first_name' =>'required',
            'last_name' =>'required',
            'birthplace' =>'required',
            'birthdate' =>'required',
            'analyses' =>'required'
        ]);
        $get_resp = app('App\Services\Calculation\CalculationService')->calculationFromUserData($req);
        return response()->json([
            'success' => true,
            'results'=> $get_resp
        ]);
    }

    function getCalculationForDashboard(Request $req){

        $req->validate([
            'first_name' =>'required',
            'last_name' =>'required',
            'birthplace' =>'required',
            'birthdate' =>'required',
            'analyses' =>'required'
        ]);
        $get_resp = app('App\Services\Calculation\CalculationService')->calculationFromUser($req);
        return response()->json([
            'success' => true,
            'results'=> $get_resp
        ]);
    }

    function getCalculationForDashboardLTA(Request $req){

        $req->validate([
            'first_name' =>'required',
            'last_name' =>'required',
            'birthplace' =>'required',
            'birthdate' =>'required',
            'analyses' =>'required'
        ]);
        $get_resp = app('App\Services\Calculation\CalculationService')->calculationFromUserLTA($req);
        return response()->json([
            'success' => true,
            'results'=> $get_resp
        ]);
    }

    function getAnalysisWithSortingCalculationsForDashboard(Request $request){

        $request->validate([
            'user' => 'required'
        ]);

        $user = User::with('userfilter:user_id,filter_type')
            ->find($request->user, ['id', 'language_id', 'first_name', 'last_name', 'gebort', 'gebdatum', 'datumcore']);

        $analyses = $this->getAllAnalyses($request);
        $request->merge([
            'first_name' => $user->first_name,
            'last_name'  => $user->last_name,
            'birthplace' => $user->gebort,
            'birthdate'  => $user->gebdatum,
            'cal_day'    => $user->datumcore,
            'analyses'   => json_encode($analyses)
        ]);
        $get_resp = app('App\Services\Calculation\CalculationService')->calculationFromUser($request);

        if ($request->header('Progressbar') == '1') { // If the request is from the progress bar
           $final_response =  collect([$get_resp, $analyses]);
        }else{
            $final_response =  $this->processChartCard($get_resp, $user);
        }

        return response()->json([
            'success' => true,
            'results' => $final_response[0],
            'labels' => $final_response[1]
        ]);
    }

    private function getAllAnalyses($request) 
    {
        // If the request is from the progress bar, return the default analyses for the progress bar
        return $request->header('Progressbar') == '1'
            ? ['Geist-Ebene', 'Emotional-Ebene', 'Mental-Ebene', 'Biochemie-Ebene', 'Körper-Ebene']
            : explode(",",trans("action.dash_right_progress", [], 'de'));
    }

    private function processChartCard($result, $user)
    {
        $sortResult = $this->userLangBaseDataProcess($result, $user);

        $allAnalysesBaseOnSort = array_map(function ($item) { // Get only the name for the chart
            return $item['name'];
        }, $sortResult);

        return collect([$sortResult, $allAnalysesBaseOnSort]); // Return the sorted result and the name of the analyses for ChartCard
    }

    private function userLangBaseDataProcess($result, $user) // Sort the result based on the user language
    {
        $language  = DB::table('languages')->where('id',$user->language_id)
                    ->pluck('short_code')->first();

        $user_language = explode(",", trans("action.dash_right_progress", [], $language ?? 'de'));
        // Use array_map to update the "name" values in the result array
        return  array_map(function ($item, $userValue) {
            $item["name"] = $userValue;
            return $item;
        }, $result->toArray(), $user_language);
    }

    /**
     * Calculate frequency for given text using FrequencyService
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function calculateFrequency(Request $request)
    {
        $text = $request->input('text', '');
        
        if (empty($text)) {
            return response()->json(['frequency' => 0]);
        }

        // Use FrequencyService for consistent calculation
        $frequencyService = app(\App\Services\Calculation\FrequencyService::class);
        $frequency = $frequencyService->calculateFrequencySafely($text, [
            'apply_transformations' => true,
            'fallback_frequency' => 0
        ]);

        return response()->json(['frequency' => $frequency]);
    }
}
